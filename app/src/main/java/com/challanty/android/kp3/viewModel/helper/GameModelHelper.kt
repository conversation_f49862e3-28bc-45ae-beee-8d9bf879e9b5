package com.challanty.android.kp3.viewModel.helper

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Canvas
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.PaintingStyle
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import com.challanty.android.kp3.data.Saved
import com.challanty.android.kp3.data.Settings
import com.challanty.android.kp3.pictureUnits.original.CelticPaths
import com.challanty.android.kp3.pictureUnits.original.CelticPicUnitFactory
import com.challanty.android.kp3.puzzle.CelticKnotPuzzle
import com.challanty.android.kp3.state.GameState
import com.challanty.android.kp3.viewModel.PicUnitDependencies
import com.challanty.android.kp3.viewModel.TileModel
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject
import kotlin.random.Random

@ViewModelScoped
class GameModelHelper @Inject constructor(
) {

    val gameTileRibbonPaint = Paint().apply {
        color = Color.Cyan // TODO: Make this a theme color
        style = PaintingStyle.Fill
        isAntiAlias = true
    }

    fun makeBoardGameState(
        puzzle: CelticKnotPuzzle,
        picUnitDependencies: PicUnitDependencies,
        lockedTileMatrix: Array<IntArray>,
        lockCnt: Int?
    ): GameState {
        val picUnitSize = picUnitDependencies.picUnitSize
        val picUnitRows = picUnitDependencies.picUnitRows
        val picUnitCols = picUnitDependencies.picUnitCols

        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols

        val tileModelList: List<TileModel> = makeTileModels(
            puzzle = puzzle,
            picUnitDependencies = picUnitDependencies,
            lockedTileMatrix = lockedTileMatrix
        )

        val boardPxSize = IntSize(
            width = (picUnitSize * picUnitCols).toInt(),
            height = (picUnitSize * picUnitRows).toInt()
        )

        val tilePxSize = IntSize(
            width = (picUnitSize * tileCols).toInt(),
            height = (picUnitSize * tileRows).toInt()
        )

        return GameState(
            showProgress = false,
            showGameBoard = true,
            lockCnt = lockCnt,
            boardPxSize = boardPxSize,
            tilePxSize = tilePxSize,
            tileModels = tileModelList,
        )
    }

    private fun makeTileModels(
        puzzle: CelticKnotPuzzle,
        picUnitDependencies: PicUnitDependencies,
        lockedTileMatrix: Array<IntArray>
    ): List<TileModel> {
        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols
        val picUnitSize = picUnitDependencies.picUnitSize
        val outlinePaint = picUnitDependencies.outlinePaint

        val (winTileRibbonPaint, winTileBGPaint) = calcWinPaints()

        val tilePxSize = IntSize(
            width = (picUnitSize * tileCols).toInt(),
            height = (picUnitSize * tileRows).toInt()
        )

        // AI says the conventional way to locate a graphic is by its center and radius.
        val drawingRadius = picUnitSize / 2
        val drawingCenter = Offset(drawingRadius, drawingRadius)

        val tileModelList = mutableListOf<TileModel>()

        var tileID = 0

        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {

                val gameBitmap = ImageBitmap(
                    width = tilePxSize.width,
                    height = tilePxSize.height
                )
                val gameTileCanvas = Canvas(gameBitmap)

                val winBitmap = ImageBitmap(
                    width = tilePxSize.width,
                    height = tilePxSize.height
                )
                val winTileCanvas = Canvas(winBitmap)

                for (tileRow in 0 until tileRows) {
                    val puzzleRow = boardRow * tileRows + tileRow

                    for (tileCol in 0 until tileCols) {
                        val puzzleCol = boardCol * tileCols + tileCol

                        val puzzlePicUnitID = puzzle.getScrambledPicUnitIDAt(
                            row = puzzleRow,
                            col = puzzleCol
                        )

                        val puzzlePicUnitStrategy =
                            CelticPicUnitFactory.createPicUnit(puzzlePicUnitID)
                        val puzzlePaths =
                            puzzlePicUnitStrategy.createPaths(drawingCenter, drawingRadius)

                        drawPicUnit(
                            canvas = gameTileCanvas,
                            paths = puzzlePaths,
                            x = tileCol * picUnitSize,
                            y = tileRow * picUnitSize,
                            ribbonPaint = gameTileRibbonPaint,
                            outlinePaint = outlinePaint
                        )

                        drawPicUnit(
                            canvas = winTileCanvas,
                            paths = puzzlePaths,
                            x = tileCol * picUnitSize,
                            y = tileRow * picUnitSize,
                            ribbonPaint = winTileRibbonPaint,
                            outlinePaint = outlinePaint,
                            bgPaint = winTileBGPaint,
                            isWin = true
                        )
                    }
                }

                tileModelList.add(
                    TileModel(
                        id = tileID++,
                        gameBitmap = gameBitmap,
                        winBitmap = winBitmap,
                        boardPosition = IntOffset(boardRow, boardCol),
                        initIntOffset = IntOffset.Zero,
                        initIntOffsetDuration = 0,
                        initQuarterTurnCnt = 0,
                        initRotationDuration = 0,
                        initIsLocked = lockedTileMatrix[boardRow][boardCol] == 1,
                        initIsSelected = false,
                        initIsWon = false
                    )
                )
            }
        }

        return tileModelList.toList()
    }

    private fun drawPicUnit(
        canvas: Canvas,
        paths: CelticPaths,
        x: Float,
        y: Float,
        ribbonPaint: Paint,
        outlinePaint: Paint,
        bgPaint: Paint = Paint(),
        isWin: Boolean = false
    ) {
        with(canvas) {
            save()
            translate(x, y)

            // It's important to draw the outline after the ribbon background since the
            // outline extends beyond the borders of the ribbon path and would be partially
            // erased by the ribbon background fill which goes all the way to the ribbon
            // path border.
            if (isWin) {
                drawPath(paths.getRibbonBGPath(), bgPaint)
            }

            drawPath(paths.getRibbonPath(), ribbonPaint)
            drawPath(paths.getOutlinePath(), outlinePaint)

            restore()
        }
    }

    private fun calcWinPaints(): Pair<Paint, Paint> {
        val random = Random(System.currentTimeMillis())

        val r = random.nextInt(0, 256)
        val g = random.nextInt(0, 256)
        val b = random.nextInt(0, 256)

        val paint1 = Paint().apply {
            color = Color(red = 255 - r, green = 255 - g, blue = 255 - b)
            style = PaintingStyle.Fill
            isAntiAlias = true
        }

        val paint2 = Paint().apply {
            color = Color(red = r, green = g, blue = b)
            style = PaintingStyle.Fill
            isAntiAlias = true
        }

        return Pair(paint1, paint2)
    }
}