package com.challanty.android.kp3.util

object Constants {
    const val VERSION = 1

    const val DEFAULT_BOARD_ROWS = 4
    const val DEFAULT_BOARD_COLS = 4
    const val DEFAULT_TILE_ROWS = 2
    const val DEFAULT_TILE_COLS = 2
    const val DEFAULT_LOCK_PERCENT = 100
    const val DEFAULT_TILES_ROTATABLE = false
    const val DEFAULT_ANIMATE = true
    const val DEFAULT_ANIMATE_ROTATION = true
    const val DEFAULT_ANIMATE_SWAP = true

    // The default game solution.
    // These are picture unit IDs for 2x2 tiles in
    // 4 tile rows and 4 tile cols.
    val DEFAULT_SOLUTION = arrayOf(
        intArrayOf(20,  9,  7,  9,  7,  9,  7, 21),
        intArrayOf( 6, 17, 18, 17, 18, 17, 18, 10),
        intArrayOf( 8, 16, 19, 16, 19, 16, 19,  4),
        intArrayOf( 6, 17, 18, 17, 18, 17, 18, 10),
        intArrayOf( 8, 16, 19, 16, 19, 16, 19,  4),
        intArrayOf( 6, 17, 18, 17, 18, 17, 18, 10),
        intArrayOf( 8, 16, 19, 16, 19, 16, 19,  4),
        intArrayOf(23,  5, 11,  5, 11,  5, 11, 22)
    )

    // The default scrambled version of DEFAULT_SOLUTION
    val DEFAULT_BOARD = arrayOf(
        intArrayOf( 7, 21,  8, 16, 19,  4, 19, 16),
        intArrayOf(18, 10,  6, 17, 18, 10, 18, 17),
        intArrayOf(19, 16, 20,  9,  8, 16, 19, 16),
        intArrayOf(18, 17,  6, 17,  6, 17, 18, 17),
        intArrayOf( 7,  9, 19,  4, 19,  4, 19, 16),
        intArrayOf(18, 17, 18, 10, 11, 22, 11,  5),
        intArrayOf(19, 16,  8, 16,  7,  9, 19, 16),
        intArrayOf(18, 17, 23,  5, 18, 17, 11,  5)
    )

    // The default tile quarter turns
    val DEFAULT_TILE_LOCKS = arrayOf(
        intArrayOf(1, 1, 1, 1),
        intArrayOf(1, 1, 1, 1),
        intArrayOf(1, 1, 1, 1),
        intArrayOf(1, 1, 1, 1),
        intArrayOf(1, 1, 1, 1),
        intArrayOf(1, 1, 1, 1),
        intArrayOf(1, 1, 1, 1),
        intArrayOf(1, 1, 1, 1)
    )

    const val MIN_DIMENSION = 1
    const val MAX_DIMENSION = 6

    val LIST_OF_ALL_DIMENSIONS = listOf(1, 2, 3, 4, 5, 6)
    val LIST_OF_ALL_DIMENSIONS_EXCEPT_1 = listOf(2, 3, 4, 5, 6)
    val LIST_OF_EVEN_DIMENSIONS = listOf(2, 4, 6)

    const val ANIMATE_SWAP_DURATION = 500
    const val ANIMATE_ROTATION_DURATION = 500

    const val PIC_UNIT_BOX_SIZE = 100f
    const val BG_PIC_UNIT_BOX_SIZE = 40f
}