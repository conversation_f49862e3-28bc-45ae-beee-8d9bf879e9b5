package com.challanty.android.kp3.ui.components

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.challanty.android.kp3.navigation.AppNavHost
import com.challanty.android.kp3.state.ProcessingStateManager
import com.challanty.android.kp3.viewModel.BackgroundViewModel
import com.challanty.android.kp3.viewModel.GameViewModel
import com.challanty.android.kp3.viewModel.RepositoryViewModel

/**
 * Root composable for the application.
 * Contains the shared background and navigation host.
 *
 * @param navController The NavHostController to use for navigation
 * @param onNavigate Callback for when navigation should occur
 * @param currentRoute The current route
 * @param exitApp Callback for when the app should exit
 */
@Composable
fun AppRoot(
    navController: NavHostController,
    onNavigate: (String) -> Unit,
    currentRoute: String,
    exitApp: () -> Unit,
) {
    // Get the ViewModels using Hilt
    val backgroundViewModel: BackgroundViewModel = hiltViewModel()
    val gameViewModel: GameViewModel = hiltViewModel()
    val repositoryViewModel: RepositoryViewModel = hiltViewModel()
    val processingStateManager: ProcessingStateManager = repositoryViewModel.processingStateManager

    // Set the background color from the theme
    backgroundViewModel.updateBackgroundColor(MaterialTheme.colorScheme.background)

    // TODO do we want all of this?
    // Provide the BackgroundViewModel via CompositionLocal for any components that need it
    CompositionLocalProvider(LocalBackgroundViewModel provides backgroundViewModel) {
        // Wrap everything with the ProcessingStateProvider to enable global input blocking
        ProcessingStateProvider(processingStateManager = processingStateManager) {
            // Use BackgroundBox to draw the background behind the NavHost
            BackgroundBox(backgroundViewModel = backgroundViewModel) {
                // Navigation host layer
                AppNavHost(
                    navController = navController,
                    onNavigate = onNavigate,
                    currentRoute = currentRoute,
                    exitApp = exitApp,
                    gameViewModel = gameViewModel
                )
            }
        }
    }
}
