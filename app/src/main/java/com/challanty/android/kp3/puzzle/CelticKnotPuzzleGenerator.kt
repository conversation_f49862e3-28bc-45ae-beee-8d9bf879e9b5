package com.challanty.android.kp3.puzzle

import com.challanty.android.kp3.pictureUnits.original.AbstractCelticPicUnit
import kotlin.random.Random

enum class KnotMakeCode(val value: Int) {
    TooManyRetries(0),
    Success(1),
    Failure(2),
    InternalError(3)
}

/**
 * A puzzle generator that creates puzzles based on boundary walls.
 *
 * This generator uses a boundary matrix to determine the pattern for each cell in the puzzle.
 * Each cell in the boundary matrix specifies the walls bounding the pattern in the corresponding
 * puzzle matrix cell.
 *
 * The generator randomly places interior walls in the boundary matrix, which determines the
 * patterns in the puzzle matrix. The pattern at each cell is determined by:
 * - The walls in the corresponding boundary cell
 * - Whether the row is even/odd
 * - Whether the column is even/odd
 *
 * The resulting puzzle forms Celtic-knot-like looping paths that may go over and under themselves
 * and each other. A puzzle is considered solved when the picture shows only looping paths with
 * no dead ends.
 */
class CelticKnotPuzzleGenerator {

    private val noWalls = 0b0000
    private val topWall = 0b0001
    private val rhtWall = 0b0010
    private val botWall = 0b0100
    private val lftWall = 0b1000

    // Convenience combinations
    private val topLftCor = topWall + lftWall
    private val topRhtCor = topWall + rhtWall
    private val botLftCor = botWall + lftWall
    private val botRhtCor = botWall + rhtWall
    private val vHallway = lftWall + rhtWall
    private val hHallway = topWall + botWall

    private val maxWallIdx = botLftCor

    // Lookup table for pattern IDs based on row and column parity (even/odd)
    // plus wall configuration.
    private val walls2ID = Array(2) { Array(2) { IntArray(maxWallIdx + 1) } }

    val rN = mutableMapOf(
        0 to listOf(2, 6, 8, 12, 20, 23, 24),
        1 to listOf(4, 15, 16),
        2 to listOf(7, 14, 19),
        3 to listOf(5, 13, 21, 22, 25),
        4 to listOf(2, 6, 8, 12, 20, 23, 24),
        5 to listOf(0, 11, 18),
        6 to listOf(3, 10, 17),
        7 to listOf(1, 9, 21, 22, 25),
        8 to listOf(4, 15, 16),
        9 to listOf(7, 14, 19),
        10 to listOf(2, 6, 8, 12, 20, 23, 24),
        11 to listOf(5, 13, 21, 22, 25),
        12 to listOf(0, 11, 18),
        13 to listOf(3, 10, 17),
        14 to listOf(2, 6, 8, 12, 20, 23, 24),
        15 to listOf(1, 9, 21, 22, 25),
        16 to listOf(7, 14, 19),
        17 to listOf(0, 11, 18),
        18 to listOf(3, 10, 17),
        19 to listOf(4, 15, 16),
        20 to listOf(1, 5, 9, 13, 21, 22, 25),
        21 to listOf(2, 6, 8, 12, 20, 23, 24),
        22 to listOf(2, 6, 8, 12, 20, 23, 24),
        23 to listOf(1, 5, 9, 13, 21, 22, 25),
        24 to listOf(2, 6, 8, 12, 20, 23, 24),
        25 to listOf(1, 5, 9, 13, 21, 22, 25),
        26 to listOf(2, 6, 8, 12, 20, 23, 24)
    )

    /**
     * bN ("bottom neighbor") maps all Celtic Knot Picture Unit IDs (PUs) plus the BORDER_ID to
     * the list all PUs that can connect to the bottom of the PU.
     */
    private val bN = mutableMapOf(
        0 to listOf(6, 14, 22, 23, 24),
        1 to listOf(3, 7, 9, 13, 20, 21, 25),
        2 to listOf(5, 12, 17),
        3 to listOf(4, 15, 16),
        4 to listOf(2, 10, 22, 23, 24),
        5 to listOf(3, 7, 9, 13, 20, 21, 25),
        6 to listOf(1, 8, 19),
        7 to listOf(0, 11, 18),
        8 to listOf(6, 14, 22, 23, 24),
        9 to listOf(5, 12, 17),
        10 to listOf(4, 15, 16),
        11 to listOf(3, 7, 9, 13, 20, 21, 25),
        12 to listOf(2, 10, 22, 23, 24),
        13 to listOf(1, 8, 19),
        14 to listOf(0, 11, 18),
        15 to listOf(3, 7, 9, 13, 20, 21, 25),
        16 to listOf(5, 12, 17),
        17 to listOf(4, 15, 16),
        18 to listOf(1, 8, 19),
        19 to listOf(0, 11, 18),
        20 to listOf(2, 6, 10, 14, 22, 23, 24),
        21 to listOf(2, 6, 10, 14, 22, 23, 24),
        22 to listOf(3, 7, 9, 13, 20, 21, 25),
        23 to listOf(3, 7, 9, 13, 20, 21, 25),
        24 to listOf(2, 6, 10, 14, 22, 23, 24),
        25 to listOf(3, 7, 9, 13, 20, 21, 25),
        26 to listOf(3, 7, 9, 13, 20, 21, 25)
    )

    /**
     * isRn and isBn are used to determine whether a PU ID can connect to the right or bottom
     * of a specific PU.
     */
    val isRn = mutableMapOf<Int, MutableMap<Int, Boolean>>()
    val isBn = mutableMapOf<Int, MutableMap<Int, Boolean>>()

    init {
        initWalls()
        initNeighbors()
    }

    fun generateWithoutWalls(puzzle: CelticKnotPuzzle): KnotMakeCode {
        // TODO fix Over/Under of generated solution
        val solution = puzzle.solution

        if (solution.isEmpty()) return KnotMakeCode.Success

        val rows = puzzle.rows
        val cols = puzzle.cols
        val seed = puzzle.seed

        // Create a random number generator, optionally with a seed
        val random = if (seed != null) Random(seed) else Random(System.currentTimeMillis())

        val hist = mutableListOf<Pair<Int, Int>>()
        val rowRetries = IntArray(rows)

        var row = 0
        var col = -1

        while (true) {
            col++
            if (col >= cols) {
                col = 0
                row++
                if (row >= rows) {
                    return KnotMakeCode.Success
                }
            }

            // Randomly select a character that fits the left neighbor.
            var remain = if (col == 0) rN[26]!!.size else rN[solution[row][col - 1]]!!.size
            var idx = random.nextInt(from = 0, until = remain)

            // Loop until a fit is found for the current space or a previous space
            while (true) {
                val choice = if (col == 0) rN[26]!![idx] else rN[solution[row][col - 1]]!![idx]

                // We already know the chosen char fits the left neighbor.
                // Does this character fit the top neighbor (whether border or knot)
                // and any existing right or bottom borders.
                val tN = if (row <= 0) 26 else solution[row - 1][col]

                if ((isBn[tN]!![choice] == true) &&
                    (col < cols - 1 || isRn[choice]!![26] == true) &&
                    (row < rows - 1 || isBn[choice]!![26] == true)
                ) {

                    // A match!  Put it into the grid.
                    // Remember where we are in the process in case we have to
                    // back up and make a change.
                    // Continue with the next grid space.
                    solution[row][col] = choice
                    hist.add(Pair(remain, idx))
                    break
                }

                // Loop until we have a new char to try for this or a previous cell
                while (true) {

                    idx++
                    remain--

                    // Wrap around if we run off the end of choices according to left neighbor
                    if (idx >= if (col == 0) rN[26]!!.size else rN[solution[row][col - 1]]!!.size) {
                        idx = 0
                    }

                    // Try new char (exit loop) if another choice is available
                    if (remain > 0) {
                        break
                    } else {
                        // Backup and loop again
                        solution[row][col] = 0 // Helps debugging, otherwise unnecessary

                        col--
                        if (col == -1) {
                            col = cols - 1
                            if (rowRetries[row]++ > 1000) {
                                return KnotMakeCode.TooManyRetries
                            }
                            row--
                            if (row == -1) {
                                return KnotMakeCode.Failure
                            }
                        }

                        if (hist.isEmpty()) {
                            return KnotMakeCode.InternalError
                        } else {
                            val pair = hist.removeAt(hist.lastIndex)
                            remain = pair.first
                            idx = pair.second
                        }
                    }
                }
            }
        }
    }

    fun generateWithWalls(puzzle: CelticKnotPuzzle) {
        val solution = puzzle.solution

        if (solution.isEmpty()) return

        val rows = puzzle.rows
        val cols = puzzle.cols
        val isFlipOU = puzzle.isFlipOU
        val seed = puzzle.seed

        // Create a random number generator, optionally with a seed
        val random = if (seed != null) Random(seed) else Random(System.currentTimeMillis())

        // Create a bounded wall matrix
        val wallMatrix = Array(rows) { row ->
            IntArray(cols) { col ->
                var walls = noWalls

                // Add outer boundary walls
                if (row == 0) walls += topWall
                if (col == 0) walls += lftWall
                if (row == rows - 1) walls += botWall
                if (col == cols - 1) walls += rhtWall

                walls
            }
        }

        // Place interior walls according to the Celtic knot strategy
        // Place walls strategically to ensure a proper puzzle
        placeInteriorWalls(wallMatrix, random)

        // Create the puzzle matrix based on the boundary matrix
        for (row in 0 until rows) {
            for (col in 0 until cols) {
                // Determine picture unit based on boundary walls and row/col parity
                solution[row][col] = determinePicUnit(wallMatrix, row, col, isFlipOU)
            }
        }
    }

    fun scramble(puzzle: CelticKnotPuzzle) {
        val solution = puzzle.solution

        if (solution.isEmpty()) return

        val scrambled = puzzle.scrambled
        val puzzleRows = puzzle.rows
        val puzzleCols = puzzle.cols
        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols
        val doRotations = puzzle.doRotations
        val seed = puzzle.seed

        // Can't scramble if the puzzle dimensions
        // are not divisible by the tile dimensions
        if (puzzleRows % tileRows != 0 || puzzleCols % tileCols != 0) {
            return
        }

        // Create a random number generator, optionally with a seed
        val random = if (seed != null) Random(seed) else Random(System.currentTimeMillis())

        // Create a list of all tile positions and...
        val shuffledPositions = mutableListOf<Pair<Int, Int>>()
        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {
                shuffledPositions.add(Pair(boardRow, boardCol))
            }
        }
        // ...shuffle them
        shuffledPositions.shuffle(random)

        val squareSize = tileRows  // Square size is the same as tile rows or columns

        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {
                val newGamePos = shuffledPositions[(boardRow * boardCols) + boardCol]
                // Calculate the rotation (0, 90, 180, or 270 degrees)
                // Only rotate if tiles are square
                val quarterTurns = if (doRotations && tileRows == tileCols) {
                    random.nextInt(4)
                } else {
                    0
                }

                // Copy and rotate the tile
                for (tileRow in 0 until squareSize) {
                    for (tileCol in 0 until squareSize) {
                        val srcRow = boardRow * squareSize + tileRow
                        val srcCol = boardCol * squareSize + tileCol

                        // Calculate the rotated position of the picture unit
                        val (rotatedTileRow, rotatedTileCol) = when (quarterTurns) {
                            0 -> Pair(tileRow, tileCol)
                            1 -> Pair(tileCol, squareSize - 1 - tileRow)
                            2 -> Pair(squareSize - 1 - tileRow, squareSize - 1 - tileCol)
                            3 -> Pair(squareSize - 1 - tileCol, tileRow)
                            else -> Pair(tileRow, tileCol) // Default to no rotation
                        }

                        // Calculate the destination position in the scrambled matrix
                        val destRow = newGamePos.first * tileRows + rotatedTileRow
                        val destCol = newGamePos.second * tileCols + rotatedTileCol

                        // Rotate the picture unit and copy it to the destination position
                        scrambled[destRow][destCol] = AbstractCelticPicUnit.Companion.rotatePicUnit(
                            picUnit = solution[srcRow][srcCol],
                            quarterTurns = quarterTurns
                        )
                    }
                }
            }
        }
    }

    fun swapTiles(
        puzzle: CelticKnotPuzzle,
        boardRow1: Int,
        boardCol1: Int,
        boardRow2: Int,
        boardCol2: Int,
    ) {
        val picUnitMatrix = puzzle.scrambled
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols

        val startRow1 = boardRow1 * tileRows
        val startCol1 = boardCol1 * tileCols
        val startRow2 = boardRow2 * tileRows
        val startCol2 = boardCol2 * tileCols

        // Swap the picture units
        for (row in 0 until tileRows) {
            for (col in 0 until tileCols) {
                val row1 = startRow1 + row
                val col1 = startCol1 + col
                val row2 = startRow2 + row
                val col2 = startCol2 + col
                val temp = picUnitMatrix[row1][col1]
                picUnitMatrix[row1][col1] = picUnitMatrix[row2][col2]
                picUnitMatrix[row2][col2] = temp
            }
        }
    }

    fun rotateTile(
        puzzle: CelticKnotPuzzle,
        boardRow: Int,
        boardCol: Int,
    ) {
        val picUnitMatrix = puzzle.scrambled
        val squareTileSize = puzzle.tileRows // Square size is the same as tile rows or columns

        val startRow = boardRow * squareTileSize
        val startCol = boardCol * squareTileSize

        // Determine the rotated tile
        val rotatedTile = Array(squareTileSize) { IntArray(squareTileSize) }
        for (row in 0 until squareTileSize) {
            for (col in 0 until squareTileSize) {
                rotatedTile[col][squareTileSize - 1 - row] =
                    AbstractCelticPicUnit.Companion.rotatePicUnit(
                        picUnitMatrix[startRow + row][startCol + col],
                        1
                    )
            }
        }

        // Replace the original tile with the rotated tile
        for (row in 0 until squareTileSize) {
            for (col in 0 until squareTileSize) {
                picUnitMatrix[startRow + row][startCol + col] = rotatedTile[row][col]
            }
        }
    }

    private fun placeInteriorWalls(
        wallMatrix: Array<IntArray>,
        random: Random
    ) {
        val rows = wallMatrix.size
        val cols = wallMatrix[0].size

        // Create a list of all possible placement positions
        var maxPlacements = 0
        val placementPositions = mutableListOf<Pair<Int, Int>>()
        for (row in 0 until rows - 1) {
            for (col in 0 until cols - 1) {
                // Only consider positions where (row + col) is odd.
                // That is, row and col are not both even or both odd.
                if ((row + col) % 2 == 1) {
                    placementPositions.add(Pair(row, col))
                    ++maxPlacements
                }
            }
        }

        // Randomly choose the number of wall placements to make (experience shows
        // between 1/3 of max and max is good)
        val numPlacements = random.nextInt(maxPlacements / 3, maxPlacements + 1)

        // Make random placements
        placementPositions.shuffle(random)
        for (i in 0 until numPlacements) {
            val (row, col) = placementPositions[i]

            // Randomly decide whether to place vertical or horizontal walls
            val isVertical = random.nextBoolean()

            if (isVertical) {
                // Place vertical walls
                wallMatrix[row][col] += rhtWall
                wallMatrix[row][col + 1] += lftWall
                wallMatrix[row + 1][col] += rhtWall
                wallMatrix[row + 1][col + 1] += lftWall
            } else {
                // Place horizontal walls
                wallMatrix[row][col] += botWall
                wallMatrix[row][col + 1] += botWall
                wallMatrix[row + 1][col] += topWall
                wallMatrix[row + 1][col + 1] += topWall
            }
        }
    }

    private fun determinePicUnit(
        wallMatrix: Array<IntArray>,
        row: Int,
        col: Int,
        isFlipOU: Boolean
    ): Int {
        // Get the walls for this cell
        val walls = wallMatrix[row][col]

        // Determine the row and column parity (even/odd)
        // If isFlipOU is true, flip the parity by adding 1 before taking modulo
        val rowParity = if (isFlipOU) (row + 1) % 2 else row % 2
        val colParity = if (isFlipOU) (col + 1) % 2 else col % 2

        // Look up the picture Unit ID in our table
        return walls2ID[rowParity][colParity][walls]
    }

    private fun initWalls() {
        // Initialize the walls2ID lookup table
        val er = 0 // even row
        val or = 1 // odd row
        val ec = 0 // even column
        val oc = 1 // odd column

        // Single walls
        walls2ID[er][oc][rhtWall] = 0  // These are pattern IDs
        walls2ID[or][oc][botWall] = 1
        walls2ID[or][ec][lftWall] = 2
        walls2ID[er][ec][topWall] = 3

        walls2ID[or][ec][rhtWall] = 4  // Opposite row/col parity of 0-3
        walls2ID[er][ec][botWall] = 5
        walls2ID[er][oc][lftWall] = 6
        walls2ID[or][oc][topWall] = 7

        walls2ID[or][oc][lftWall] = 8
        walls2ID[or][ec][topWall] = 9
        walls2ID[er][ec][rhtWall] = 10
        walls2ID[er][oc][botWall] = 11

        walls2ID[er][ec][lftWall] = 12  // Opposite row/col parity of 8-11
        walls2ID[er][oc][topWall] = 13
        walls2ID[or][oc][rhtWall] = 14
        walls2ID[or][ec][botWall] = 15

        // No walls
        walls2ID[or][ec][noWalls] = 16
        walls2ID[er][ec][noWalls] = 17
        walls2ID[er][oc][noWalls] = 18
        walls2ID[or][oc][noWalls] = 19

        // Corner walls
        walls2ID[er][ec][topLftCor] = 20
        walls2ID[er][oc][topLftCor] = 20
        walls2ID[or][oc][topLftCor] = 20
        walls2ID[or][ec][topLftCor] = 20

        walls2ID[er][ec][topRhtCor] = 21
        walls2ID[er][oc][topRhtCor] = 21
        walls2ID[or][oc][topRhtCor] = 21
        walls2ID[or][ec][topRhtCor] = 21

        walls2ID[er][ec][botRhtCor] = 22
        walls2ID[er][oc][botRhtCor] = 22
        walls2ID[or][oc][botRhtCor] = 22
        walls2ID[or][ec][botRhtCor] = 22

        walls2ID[er][ec][botLftCor] = 23
        walls2ID[er][oc][botLftCor] = 23
        walls2ID[or][oc][botLftCor] = 23
        walls2ID[or][ec][botLftCor] = 23

        // Hallways
        walls2ID[er][ec][vHallway] = 24
        walls2ID[er][oc][vHallway] = 24
        walls2ID[or][oc][vHallway] = 24
        walls2ID[or][ec][vHallway] = 24

        walls2ID[er][ec][hHallway] = 25
        walls2ID[er][oc][hHallway] = 25
        walls2ID[or][oc][hHallway] = 25
        walls2ID[or][ec][hHallway] = 25
    }

    private fun initNeighbors() {
        // Use the list of all valid right and bottom PUs to initialize isRn and isBn
        for (i in rN.keys) {
            val rnList = rN[i]!!
            val myMap = rnList.associateWith { true }
            isRn[i] = myMap.toMutableMap()
        }

        for (i in bN.keys) {
            val bnList = bN[i]!!
            val myMap = bnList.associateWith { true }
            isBn[i] = myMap.toMutableMap()
        }

        // Since BORDER_ID is not a valid knot PU it isn't in the rN and bN lists
        // it didn't get included in the above loops even though the BORDER_ID is a
        // valid neighbor for various PUs. We manually specify the valid neighbors
        // here.
        isRn[0]!![26] = true
        isRn[4]!![26] = true
        isRn[10]!![26] = true
        isRn[14]!![26] = true
        isRn[21]!![26] = true
        isRn[22]!![26] = true
        isRn[24]!![26] = true

        isBn[1]!![26] = true
        isBn[5]!![26] = true
        isBn[11]!![26] = true
        isBn[15]!![26] = true
        isBn[22]!![26] = true
        isBn[23]!![26] = true
        isBn[25]!![26] = true
    }
}